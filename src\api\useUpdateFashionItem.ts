import { useState } from "react";
import useAuth from "./useAuth";
import { UMBRACO_ADRESS } from "../constants/urls";

const useUpdateFashionItem = () => {
  const { token, fetchToken } = useAuth();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);

  const updateFashionItem = async (
    id: string,
    data: {
      title: string;
      description: string;
      photographer: string;
      date: string;
      city: string;
      other: string;
      facebook: string;
      mail: string;
      mediaKey: string;
    }
  ) => {
    setLoading(true);
    setError(null);
    setSuccess(false);

    let currentToken = token;
    if (!currentToken) {
      await fetchToken();
      currentToken = localStorage.getItem("authToken") || "";
      if (!currentToken) {
        setError("Missing authentication token after refresh.");
        setLoading(false);
        return;
      }
    }

    const requestBody = {
      values: [
        {
          editorAlias: "Umbraco.MediaPicker3",
          alias: "shopPagePhoto",
          culture: null,
          segment: null,
          value: [
            {
              key: id,
              mediaKey: data.mediaKey,
              mediaTypeAlias: "",
              crops: [],
              focalPoint: null,
            },
          ],
        },
        {
          editorAlias: "Umbraco.TextBox",
          alias: "models",
          culture: null,
          segment: null,
          value: data.title,
        },
        {
          editorAlias: "Umbraco.TextBox",
          alias: "date",
          culture: null,
          segment: null,
          value: data.date,
        },
        {
          editorAlias: "Umbraco.TextBox",
          alias: "location",
          culture: null,
          segment: null,
          value: data.city,
        },
        {
          editorAlias: "Umbraco.TextBox",
          alias: "description",
          culture: null,
          segment: null,
          value: data.description,
        },
        {
          editorAlias: "Umbraco.TextBox",
          alias: "socials",
          culture: null,
          segment: null,
          value: data.other,
        },
        {
          editorAlias: "Umbraco.TextBox",
          alias: "facebook",
          culture: null,
          segment: null,
          value: data.facebook,
        },
        {
          editorAlias: "Umbraco.TextBox",
          alias: "mail",
          culture: null,
          segment: null,
          value: data.mail,
        },
        {
          editorAlias: "Umbraco.TextBox",
          alias: "photographer",
          culture: null,
          segment: null,
          value: data.photographer,
        },
      ],
      variants: [
        {
          culture: "en-US",
          segment: null,
          state: "PublishedPendingChanges",
          name: data.title,
        },
      ],
      template: {
        id: "b4f97ab6-ad89-4aa8-9f3a-f42a17d3eac3",
      },
    };

    try {
      let response = await fetch(
        `${UMBRACO_ADRESS}umbraco/management/api/v1/document/${id}`,
        {
          method: "PUT",
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${currentToken}`,
          },
          body: JSON.stringify(requestBody),
        }
      );

      if (!response.ok) {
        if (response.status === 401) {
          await fetchToken();
          currentToken = localStorage.getItem("authToken") || "";
          response = await fetch(
            `${UMBRACO_ADRESS}umbraco/management/api/v1/document/${id}`,
            {
              method: "PUT",
              headers: {
                "Content-Type": "application/json",
                Authorization: `Bearer ${currentToken}`,
              },
              body: JSON.stringify(requestBody),
            }
          );
        }
        if (!response.ok) {
          throw new Error(`Błąd: ${response.statusText}`);
        }
      }

      setSuccess(true);
    } catch (err: any) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  return { updateFashionItem, loading, error, success };
};

export default useUpdateFashionItem;
