import { useQuery } from "@tanstack/react-query";
import { UMBRACO_ADRESS } from "../constants/urls";

const fetchAddShopPage = async () => {
  try {
    const response = await fetch(
      `${UMBRACO_ADRESS}umbraco/api/Document/GetAllDocuments?documentType=addShopPage&pageSize=200
`
    );

    if (!response.ok) {
      console.error("Błąd API:", response.status, response.statusText);
      throw new Error(`Błąd API: ${response.status} - ${response.statusText}`);
    }

    const data = await response.json();

    return data?.data ?? [];
  } catch (error) {
    console.error("Błąd pobierania danych:", error);
    return [];
  }
};

const useGetShopPageDataWithFilters = () => {
  return useQuery({
    queryKey: ["addShopPage"],
    queryFn: fetchAddShopPage,
  });
};

export default useGetShopPageDataWithFilters;
