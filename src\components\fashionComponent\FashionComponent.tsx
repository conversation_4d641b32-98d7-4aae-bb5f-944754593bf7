import styles from "./fashionComponent.module.scss";
import DeleteIcon from "@mui/icons-material/Delete";
import EditIcon from "@mui/icons-material/Edit";
import RemoveRedEyeIcon from "@mui/icons-material/RemoveRedEye";
import useDeleteDocument from "../../api/useDeleteDocument";
import DoneIcon from "@mui/icons-material/Done";
import { Button, TextareaAutosize, TextField, Typography } from "@mui/material";
import { useState } from "react";
import SidebarForm from "./SidePanel";
import DeleteConfirmationModal from "../deleteConfirmationModal/DeleteConfirmationModal";
import SaveConfirmationModal from "../saveConfirmationModal/SaveConfirmationModal";
import useUpdateFashionItem from "../../api/useUpdateFashionItem";
import { useQueryClient } from "@tanstack/react-query";
import { useNavigate } from "react-router-dom";
import addPhotoIcon from "../../assets/images/upload-photo.png";
import { createAndUpload } from "../../api/useCreateAndUpload";
import { UMBRACO_ADRESS } from "../../constants/urls";
import usePublishDocument from "../../api/usePublishDocument";
import Loader from "../loader/Loader";

const commonInputSx = {
  "& .MuiOutlinedInput-root": {
    color: "#4D4D4D",
    fontFamily: "Poppins, sans-serif",
    fontSize: "16px",
    backgroundColor: "transparent",
    "& fieldset": {
      borderColor: "#808080",
    },
    "&:hover fieldset": {
      borderColor: "#808080",
    },
    "&.Mui-focused fieldset": {
      borderColor: "#4D4D4D",
    },
  },
};

const FashionComponent = ({ item }: any) => {
  const shopPagePhoto = item?.properties?.shopPagePhoto || [];

  const [title, setTitle] = useState(item?.name || "");
  const [urlSlug, setUrlSlug] = useState(item?.route?.path || "");
  const [desc, setDesc] = useState(item?.properties?.description || "");
  const [photographer, setPhotographer] = useState(
    item?.properties?.photographer || ""
  );

  const [fullName, setFullName] = useState("");
  const [date, setDate] = useState(item?.properties?.date || "");
  const [city, setCity] = useState(item?.properties?.location || "");
  const [mail, setMail] = useState(item?.properties?.mail || "");
  const [ig, setIg] = useState("");
  const [fb, setFb] = useState(item?.properties?.facebook || "");
  const [tiktok, setTiktok] = useState("");
  const [other, setOther] = useState(item?.properties?.socials || "");
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState<boolean>(false);
  const [isSaveModalOpen, setIsSaveModalOpen] = useState<boolean>(false);
  const [isSidebarOpen, setSidebarOpen] = useState(false);

  const [image, setImage] = useState<File | null>(null);
  const [dragging, setDragging] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);

  const { deleteDocument } = useDeleteDocument();
  const { updateFashionItem, success: updateSuccess } = useUpdateFashionItem();
  const { publishDocument } = usePublishDocument();

  const navigate = useNavigate();
  const queryClient = useQueryClient();
  const handleDelete = async () => {
    try {
      setIsDeleteModalOpen(false);
      if (item.id) {
        await deleteDocument(item.id);
        queryClient.invalidateQueries({ queryKey: ["addShopPage"] });
        navigate("/");
      }
    } catch (error) {
      console.error("Error deleting photo:", error);
    }
  };

  const handleOpenSidebar = () => setSidebarOpen(true);
  const handleCloseSidebar = () => setSidebarOpen(false);

  const handleSave = async () => {
    setIsLoading(true);
    try {
      const updatedData = {
        title: title,
        description: desc,
        photographer: photographer,
        date: date,
        city: city,
        other: other,
        facebook: fb,
        mail: mail,
        mediaKey: shopPagePhoto?.[0]?.id || "",
      };
      if (item.id) {
        await updateFashionItem(item.id, updatedData);
        await publishDocument(item.id);
      } else {
        if (image) {
          var resp = await createAndUpload({ file: image, name: title });
          console.log("resp");
          console.log(resp);
          if (resp.documentKey && resp.mediaKey) {
            updatedData.mediaKey = resp.mediaKey;
            console.log("updatedData");
            console.log(updatedData);
            await updateFashionItem(resp.documentKey, updatedData);
            await publishDocument(resp.documentKey);
          } else {
            setError("Error creating document");
          }
        }
      }

      setIsSaveModalOpen(true);
      setIsLoading(false);
      queryClient.invalidateQueries({ queryKey: ["addShopPage"] });
      queryClient.invalidateQueries({ queryKey: ["getUmbracoContent"] });
    } catch (error) {
      setIsLoading(false);
      console.error("Error updating photo item:", error);
    }
  };

  const handleDragOver = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    setDragging(true);
  };

  const handleDragLeave = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    setDragging(false);
  };

  const handleDrop = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    setDragging(false);

    const file = e.dataTransfer.files[0];
    if (file && file.type.startsWith("image/")) {
      setImage(file);
    }
  };

  const handleFileUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file && file.type.startsWith("image/")) {
      setImage(file);
    }
  };

  return (
    <>
      {isLoading && <Loader />}
      <div className={styles.fashionComponent}>
        <div className={styles.header}>
          {item.id ? (
            <div>
              <div
                className={`${styles.button}`}
                onClick={() => setIsDeleteModalOpen(true)}
              >
                <DeleteIcon /> <p>Delete</p>
              </div>
              <div
                className={`${styles.button}`}
                onClick={() =>
                  window.open(
                    "https://traffique.astroid.com.pl" + item.route.path,
                    "_blank"
                  )
                }
              >
                <RemoveRedEyeIcon /> <p>Preview</p>
              </div>
            </div>
          ) : (
            <div></div>
          )}

          <div>
            <div
              className={`${styles.button} ${styles.save}`}
              onClick={handleSave}
            >
              <DoneIcon /> <p>Save</p>
            </div>
          </div>
        </div>
        <div className={styles.container}>
          <div className={styles.containerWithImage}>
            <div className={styles.leftSide}>
              {shopPagePhoto?.length > 0 || image != null ? (
                <img
                  src={
                    image != null
                      ? URL.createObjectURL(image)
                      : UMBRACO_ADRESS + shopPagePhoto[0].url
                  }
                  alt="Fashion"
                />
              ) : (
                <div
                  className={`upload-container ${dragging ? "dragging" : ""}`}
                  onDragOver={handleDragOver}
                  onDragLeave={handleDragLeave}
                  onDrop={handleDrop}
                >
                  <img
                    className={styles.uploadIcon}
                    src={addPhotoIcon}
                    alt="Upload photo"
                  />
                  <label className={styles.fileUpload}>
                    <input
                      type="file"
                      accept="image/*"
                      onChange={handleFileUpload}
                    />
                    Drag photo here or upload a file
                  </label>
                </div>
              )}
            </div>
            <div className={styles.rightSide}>
              <Typography variant="body1">Title</Typography>
              <TextField
                fullWidth
                variant="outlined"
                sx={{ mb: 2, ...commonInputSx }}
                value={title}
                onChange={(e) => setTitle(e.target.value)}
              />
              <Typography variant="body1">URL Slug</Typography>
              <TextField
                fullWidth
                variant="outlined"
                sx={{ mb: 2, ...commonInputSx }}
                value={urlSlug}
                onChange={(e) => setUrlSlug(e.target.value)}
              />
              <Typography variant="body1">Description</Typography>
              <TextareaAutosize
                minRows={6}
                value={desc}
                onChange={(e) => setDesc(e.target.value)}
              />
              <Typography variant="body1">Photographer</Typography>
              <TextField
                fullWidth
                variant="outlined"
                sx={{ mb: 2, ...commonInputSx }}
                value={photographer}
                onChange={(e) => setPhotographer(e.target.value)}
              />
            </div>
          </div>
          <div className={styles.bottom}>
            <TextField
              placeholder="Full Name"
              fullWidth
              variant="outlined"
              sx={{ mb: 2, ...commonInputSx }}
              value={fullName}
              onChange={(e) => setFullName(e.target.value)}
            />
            <TextField
              placeholder="Date"
              fullWidth
              variant="outlined"
              sx={{ mb: 2, ...commonInputSx }}
              value={date}
              onChange={(e) => setDate(e.target.value)}
            />
            <TextField
              placeholder="City"
              fullWidth
              variant="outlined"
              sx={{ mb: 2, ...commonInputSx }}
              value={city}
              onChange={(e) => setCity(e.target.value)}
            />
            <TextField
              placeholder="Mail"
              fullWidth
              variant="outlined"
              sx={{ mb: 2, ...commonInputSx }}
              value={mail}
              onChange={(e) => setMail(e.target.value)}
            />
            <TextField
              placeholder="@IG"
              fullWidth
              variant="outlined"
              sx={{ mb: 2, ...commonInputSx }}
              value={ig}
              onChange={(e) => setIg(e.target.value)}
            />
            <TextField
              placeholder="@FB"
              fullWidth
              variant="outlined"
              sx={{ mb: 2, ...commonInputSx }}
              value={fb}
              onChange={(e) => setFb(e.target.value)}
            />
            <TextField
              placeholder="@TIKTOK"
              fullWidth
              variant="outlined"
              sx={{ mb: 2, ...commonInputSx }}
              value={tiktok}
              onChange={(e) => setTiktok(e.target.value)}
            />
            <TextField
              placeholder="@Other"
              fullWidth
              variant="outlined"
              sx={{ mb: 2, ...commonInputSx }}
              value={other}
              onChange={(e) => setOther(e.target.value)}
            />
          </div>
          <div className={styles.itemsConteiner}>
            <p className={styles.title}>ADD ITEM</p>
            <div className={styles.item}>
              <div className={styles.leftSide}>
                <p>Item</p>
              </div>
              <div className={styles.rightSide}>
                <Button onClick={handleOpenSidebar}>
                  <EditIcon />
                </Button>
                <Button>
                  <DeleteIcon />
                </Button>
              </div>
            </div>
            <div className={styles.addItem}>Add Item</div>
          </div>
        </div>
      </div>
      <SaveConfirmationModal
        open={isSaveModalOpen}
        onClose={() => setIsSaveModalOpen(false)}
        success={updateSuccess || error === null}
      />
      <DeleteConfirmationModal
        open={isDeleteModalOpen}
        onClose={() => setIsDeleteModalOpen(false)}
        onConfirm={handleDelete}
      />
      <SidebarForm open={isSidebarOpen} onClose={handleCloseSidebar} />
    </>
  );
};

export default FashionComponent;
