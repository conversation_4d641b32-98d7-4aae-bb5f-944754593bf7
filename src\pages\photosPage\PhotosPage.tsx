import { useParams } from "react-router-dom";
import FashionComponent from "../../components/fashionComponent/FashionComponent";
import SearchBar from "../../components/searchBar/SearchBar";
import Sidebar from "../../components/sideBar/SideBar";
import styles from "./photosPage.module.scss";
import { useGetUmbracoContentQuery } from "../../api/useGetUmbracoContentQuery";
import Loader from "../../components/loader/Loader";
import MessageAlert from "../../components/messageAlert/MessageAlert";

const PhotosPage = () => {
    const { id } = useParams();
    const isEditMode = Boolean(id);
    
    const { data: item, isLoading, error } = useGetUmbracoContentQuery(id!, {
        enabled: isEditMode,
        queryKey: [],
    });

    const currentItem = id ? item : {};

    if (isLoading) return <Loader />;
    if (error) return <MessageAlert type="error" message="Error loading photo data" />;

    return (
        <div className={styles.photosPage}>
            <SearchBar />
            <div className={styles.container}>
                <Sidebar />
                <FashionComponent item={currentItem} />
            </div>
        </div>
    );
};

export default PhotosPage;
