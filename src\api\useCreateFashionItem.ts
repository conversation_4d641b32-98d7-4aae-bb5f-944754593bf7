import { useState } from "react";
import useAuth from "./useAuth";
import { UMBRACO_ADRESS } from "../constants/urls";

const useCreateFashionItem = () => {
  const { token, fetchToken } = useAuth();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);

  const createFashionItem = async (data: {
    title: string;
    description: string;
    photographer: string;
    date: string;
    city: string;
    other: string;
    facebook: string;
    mail: string;
  }) => {
    setLoading(true);
    setError(null);
    setSuccess(false);

    let currentToken = token;
    if (!currentToken) {
      await fetchToken();
      currentToken = localStorage.getItem("authToken") || "";
      if (!currentToken) {
        setError("Missing authentication token after refresh.");
        setLoading(false);
        return;
      }
    }

    const requestBody = {
      values: [
        /*  {
          editorAlias: "Umbraco.MediaPicker3",
          alias: "shopPagePhoto",
          culture: null,
          segment: null,
          value: [
            {
              key: "5aa1bbb5-5e9e-4e18-9db7-b34a9183f1b7",
              mediaKey: "fc682289-6b04-4237-a9da-c654ca51671a",
              mediaTypeAlias: "",
              crops: [],
              focalPoint: null,
            },
          ],
        },*/
        {
          editorAlias: "Umbraco.TextBox",
          alias: "models",
          culture: null,
          segment: null,
          value: data.title,
        },
        {
          editorAlias: "Umbraco.TextBox",
          alias: "date",
          culture: null,
          segment: null,
          value: data.date,
        },
        {
          editorAlias: "Umbraco.TextBox",
          alias: "location",
          culture: null,
          segment: null,
          value: data.city,
        },
        {
          editorAlias: "Umbraco.TextBox",
          alias: "description",
          culture: null,
          segment: null,
          value: data.description,
        },
        {
          editorAlias: "Umbraco.TextBox",
          alias: "socials",
          culture: null,
          segment: null,
          value: data.other,
        },
        {
          editorAlias: "Umbraco.TextBox",
          alias: "facebook",
          culture: null,
          segment: null,
          value: data.facebook,
        },
        {
          editorAlias: "Umbraco.TextBox",
          alias: "mail",
          culture: null,
          segment: null,
          value: data.mail,
        },
        {
          editorAlias: "Umbraco.TextBox",
          alias: "photographer",
          culture: null,
          segment: null,
          value: data.photographer,
        },
      ],
      variants: [
        {
          culture: "en-US",
          //segment: null,
          //  state: null,
          name: data.title,
          //   publishDate: null,
          //  createDate: null,
          //updateDate: null,
        },
      ],
      template: {
        id: "b4f97ab6-ad89-4aa8-9f3a-f42a17d3eac3",
      },
      parent: {
        id: "d482ff55-d7d9-4da8-a4ad-7b29ffcd535f",
      },
      documentType: {
        id: "bdaf09be-c27b-4347-ac8d-ba8a85d77fde",
      },
    };

    try {
      let response = await fetch(
        `${UMBRACO_ADRESS}umbraco/management/api/v1/document`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${currentToken}`,
          },
          body: JSON.stringify(requestBody),
        }
      );

      if (!response.ok) {
        if (response.status === 401) {
          await fetchToken();
          currentToken = localStorage.getItem("authToken") || "";
          response = await fetch(
            `${UMBRACO_ADRESS}umbraco/management/api/v1/document`,
            {
              method: "POST",
              headers: {
                "Content-Type": "application/json",
                Authorization: `Bearer ${currentToken}`,
              },
              body: JSON.stringify(requestBody),
            }
          );
        }
        if (!response.ok) {
          throw new Error(`Błąd: ${response.statusText}`);
        }
      }
      setSuccess(true);
    } catch (err: any) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  return { createFashionItem, loading, error, success };
};

export default useCreateFashionItem;
